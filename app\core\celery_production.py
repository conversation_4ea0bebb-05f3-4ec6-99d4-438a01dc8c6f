"""
Celery Configuration for Production (100+ concurrent users)
Cấu h<PERSON>y tối ưu cho môi trường production với khả năng xử lý cao
"""

import os
import sys
from celery import Celery
from kombu import Queue, Exchange
from celery.signals import worker_ready, worker_shutdown
import logging

# Add project root to Python path
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.core.config import settings

logger = logging.getLogger(__name__)

# Tạo Celery instance với production settings
celery_app = Celery("planbook_ai_production")

# Production broker configuration (RabbitMQ recommended)
BROKER_URL = os.getenv("CELERY_BROKER_URL", "amqp://admin:password@rabbitmq:5672//")
RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "redis://redis-master:6379/0")

# Define exchanges for better routing
pdf_exchange = Exchange('pdf_tasks', type='direct')
embeddings_exchange = Exchange('embeddings_tasks', type='direct')
cv_exchange = Exchange('cv_tasks', type='direct')
general_exchange = Exchange('general_tasks', type='direct')

# Production Celery configuration
celery_app.conf.update(
    # Broker và Result Backend
    broker_url=BROKER_URL,
    result_backend=RESULT_BACKEND,
    
    # Connection settings
    broker_connection_retry_on_startup=True,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
    
    # Serialization
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    
    # Timezone
    timezone='Asia/Ho_Chi_Minh',
    enable_utc=True,
    
    # Task routing với exchanges
    task_routes={
        'app.tasks.pdf_tasks.*': {
            'queue': 'pdf_queue',
            'exchange': 'pdf_tasks',
            'routing_key': 'pdf.heavy'
        },
        'app.tasks.embeddings_tasks.*': {
            'queue': 'embeddings_queue', 
            'exchange': 'embeddings_tasks',
            'routing_key': 'embeddings.fast'
        },
        'app.tasks.cv_tasks.*': {
            'queue': 'cv_queue',
            'exchange': 'cv_tasks', 
            'routing_key': 'cv.medium'
        },
        'app.tasks.slide_generation_tasks.*': {
            'queue': 'slide_generation_queue',
            'exchange': 'general_tasks',
            'routing_key': 'slides.medium'
        },
        'app.tasks.lesson_plan_tasks.*': {
            'queue': 'general_queue',
            'exchange': 'general_tasks',
            'routing_key': 'general.fast'
        },
        'app.tasks.smart_exam_tasks.*': {
            'queue': 'general_queue',
            'exchange': 'general_tasks',
            'routing_key': 'general.medium'
        },
        'app.tasks.guide_tasks.*': {
            'queue': 'general_queue',
            'exchange': 'general_tasks',
            'routing_key': 'general.fast'
        },
    },
    
    # Queue definitions với priority
    task_queues=(
        Queue('pdf_queue', pdf_exchange, routing_key='pdf.heavy', 
              queue_arguments={'x-max-priority': 10}),
        Queue('embeddings_queue', embeddings_exchange, routing_key='embeddings.fast',
              queue_arguments={'x-max-priority': 8}),
        Queue('cv_queue', cv_exchange, routing_key='cv.medium',
              queue_arguments={'x-max-priority': 6}),
        Queue('slide_generation_queue', general_exchange, routing_key='slides.medium',
              queue_arguments={'x-max-priority': 5}),
        Queue('general_queue', general_exchange, routing_key='general.fast',
              queue_arguments={'x-max-priority': 7}),
    ),
    
    # Task execution settings - Production optimized
    task_acks_late=True,  # Acknowledge after task completion
    worker_prefetch_multiplier=2,  # Prefetch 2 tasks per worker
    task_reject_on_worker_lost=True,  # Reject tasks if worker dies
    
    # Result settings
    result_expires=7200,  # 2 hours
    result_persistent=True,  # Persist results
    result_compression='gzip',  # Compress results
    
    # Task time limits
    task_soft_time_limit=1800,  # 30 minutes soft limit
    task_time_limit=2400,  # 40 minutes hard limit
    
    # Worker settings - Production optimized
    worker_max_tasks_per_child=200,  # Restart worker after 200 tasks
    worker_disable_rate_limits=False,  # Enable rate limiting
    worker_max_memory_per_child=500000,  # 500MB memory limit per child
    
    # Monitoring và logging
    worker_send_task_events=True,  # Enable task events
    task_send_sent_event=True,  # Send task sent events
    
    # Task priority support
    task_inherit_parent_priority=True,
    task_default_priority=5,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute default retry delay
    task_max_retries=3,
    
    # Security
    worker_hijack_root_logger=False,
    worker_log_color=False,
    
    # Performance optimizations
    task_compression='gzip',
    task_ignore_result=False,
    
    # Include tasks
    include=[
        'app.tasks.pdf_tasks',
        'app.tasks.embeddings_tasks', 
        'app.tasks.cv_tasks',
        'app.tasks.lesson_plan_tasks',
        'app.tasks.smart_exam_tasks',
        'app.tasks.slide_generation_tasks',
        'app.tasks.guide_tasks',
    ],
)

# Auto-discover tasks
celery_app.autodiscover_tasks([
    'app.tasks',
])

# Production task decorator với advanced retry logic
def production_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    priority=5,
    **kwargs
):
    """Production task decorator với advanced features"""
    def decorator(func):
        return celery_app.task(
            bind=bind,
            autoretry_for=autoretry_for,
            retry_kwargs=retry_kwargs,
            priority=priority,
            **kwargs
        )(func)
    return decorator

# Worker lifecycle hooks
@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Called when worker is ready"""
    logger.info(f"🚀 Production worker {sender.hostname} is ready")

@worker_shutdown.connect  
def worker_shutdown_handler(sender=None, **kwargs):
    """Called when worker shuts down"""
    logger.info(f"🛑 Production worker {sender.hostname} is shutting down")

# Health check task
@celery_app.task(name='health_check', queue='general_queue')
def health_check():
    """Simple health check task"""
    return {'status': 'healthy', 'timestamp': 'now'}

# User session tracking
@celery_app.task(bind=True, name='track_user_session')
def track_user_session(self, user_id: str, session_id: str, action: str):
    """Track user session for analytics"""
    logger.info(f"User {user_id} session {session_id}: {action}")
    return {'user_id': user_id, 'session_id': session_id, 'action': action}

logger.info(f"🔧 Production Celery app configured with broker: {BROKER_URL}")
logger.info(f"🔧 Result backend: {RESULT_BACKEND}")
